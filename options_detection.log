2025-07-28 15:25:04,658 - __main__ - INFO - Starting in API-only mode
2025-07-28 15:25:05,918 - api.main - INFO - Starting Options Manipulation Detection API
2025-07-28 15:25:05,967 - utils.database - ERROR - Failed to initialize database: Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/NullPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.
2025-07-28 15:25:05,967 - core.detection_engine - ERROR - Failed to initialize detection engine: Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/NullPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.
2025-07-28 15:25:05,968 - api.main - ERROR - Failed to initialize detection engine: Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/NullPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.
2025-07-28 15:25:52,942 - __main__ - INFO - Starting in API-only mode
2025-07-28 15:25:53,760 - api.main - INFO - Starting Options Manipulation Detection API
2025-07-28 15:25:53,813 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 15:25:53,815 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("manipulation_signals")
2025-07-28 15:25:53,815 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 15:25:53,822 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("options_data")
2025-07-28 15:25:53,823 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 15:25:53,825 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("system_metrics")
2025-07-28 15:25:53,825 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 15:25:53,826 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("system_metrics")
2025-07-28 15:25:53,826 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 15:25:53,829 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE system_metrics (
	id INTEGER NOT NULL, 
	timestamp DATETIME NOT NULL, 
	data_points_collected INTEGER, 
	collection_errors INTEGER, 
	collection_latency_ms FLOAT, 
	detection_cycles_completed INTEGER, 
	total_signals_generated INTEGER, 
	high_confidence_signals INTEGER, 
	memory_usage_mb FLOAT, 
	cpu_usage_percent FLOAT, 
	active_connections INTEGER, 
	PRIMARY KEY (id)
)


2025-07-28 15:25:53,830 - sqlalchemy.engine.Engine - INFO - [no key 0.00108s] ()
2025-07-28 15:25:53,839 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 15:25:53,840 - utils.database - INFO - Database manager initialized successfully
2025-07-28 15:25:53,869 - utils.cache - INFO - Cache manager initialized successfully
2025-07-28 15:25:53,871 - data_collectors.nse_collector - INFO - NSE data collector session started
2025-07-28 15:25:53,884 - utils.metrics - INFO - Prometheus metrics server started on port 8000
2025-07-28 15:25:53,884 - core.detection_engine - INFO - Detection engine initialized successfully
2025-07-28 15:25:53,884 - api.main - INFO - Detection engine initialized
2025-07-28 15:25:53,886 - api.main - INFO - Shutting down Options Manipulation Detection API
2025-07-28 15:25:53,886 - data_collectors.nse_collector - INFO - NSE data collector session closed
2025-07-28 15:25:53,887 - utils.database - INFO - Database connections closed
2025-07-28 15:25:53,888 - utils.cache - INFO - Cache connection closed
2025-07-28 15:25:53,888 - core.detection_engine - INFO - Detection engine shutdown complete
