"""
Data models for the Options Manipulation Detection System
"""
from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Dict, Optional, Any
from enum import Enum
from pydantic import BaseModel, Field
import uuid

class PatternType(str, Enum):
    """Types of manipulation patterns"""
    ORDER_SPOOFING = "order_spoofing"
    OPTIONS_PINNING = "options_pinning"
    GAMMA_SQUEEZE_SETUP = "gamma_squeeze_setup"
    VOLATILITY_SKEW_MANIPULATION = "volatility_skew_manipulation"
    CROSS_ASSET_MANIPULATION = "cross_asset_manipulation"
    TIME_BASED_MANIPULATION = "time_based_manipulation"
    INSTITUTIONAL_FLOW_DISGUISE = "institutional_flow_disguise"

class OptionType(str, Enum):
    """Option types"""
    CALL = "CE"
    PUT = "PE"

class ConfidenceLevel(str, Enum):
    """Confidence levels for signals"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ManipulationSignal:
    """Represents a detected manipulation pattern"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    pattern_type: PatternType
    timestamp: datetime
    symbols_affected: List[str]
    confidence: float
    description: str
    estimated_profit: float
    market_impact: Dict[str, Any]
    
    # Additional metadata
    detection_algorithm: str = ""
    raw_data: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def confidence_level(self) -> ConfidenceLevel:
        """Get confidence level based on confidence score"""
        if self.confidence >= 0.9:
            return ConfidenceLevel.CRITICAL
        elif self.confidence >= 0.8:
            return ConfidenceLevel.HIGH
        elif self.confidence >= 0.6:
            return ConfidenceLevel.MEDIUM
        else:
            return ConfidenceLevel.LOW

class OptionsData(BaseModel):
    """Options chain data model"""
    symbol: str
    expiry_date: datetime
    strike: float
    option_type: OptionType
    
    # Pricing data
    last_price: float
    bid_price: float
    ask_price: float
    
    # Volume and OI
    volume: int
    open_interest: int
    bid_qty: int
    ask_qty: int
    
    # Greeks
    delta: Optional[float] = None
    gamma: Optional[float] = None
    theta: Optional[float] = None
    vega: Optional[float] = None
    implied_volatility: Optional[float] = None
    
    # Metadata
    timestamp: datetime
    change: Optional[float] = None
    percent_change: Optional[float] = None

class OrderBookData(BaseModel):
    """Order book data model"""
    symbol: str
    strike: float
    option_type: OptionType
    timestamp: datetime
    
    # Bid side
    bid_prices: List[float]
    bid_quantities: List[int]
    
    # Ask side
    ask_prices: List[float]
    ask_quantities: List[int]
    
    # Last trade
    last_trade_price: float
    last_trade_qty: int

class TradeData(BaseModel):
    """Individual trade data model"""
    symbol: str
    strike: float
    option_type: OptionType
    timestamp: datetime
    
    trade_price: float
    trade_qty: int
    trade_value: float
    
    # Market impact
    price_change: float
    volume_impact: float

class MarketData(BaseModel):
    """Aggregated market data"""
    symbol: str
    timestamp: datetime
    
    # Underlying data
    underlying_price: float
    underlying_change: float
    underlying_volume: int
    
    # Options summary
    total_call_oi: int
    total_put_oi: int
    total_call_volume: int
    total_put_volume: int
    
    # Market indicators
    pcr_oi: float  # Put-Call Ratio by OI
    pcr_volume: float  # Put-Call Ratio by Volume
    max_pain: Optional[float] = None
    
class DetectionResult(BaseModel):
    """Result from a detection algorithm"""
    algorithm_name: str
    execution_time: float
    signals_found: int
    signals: List[ManipulationSignal]
    errors: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)

class AlertConfig(BaseModel):
    """Alert configuration"""
    pattern_types: List[PatternType]
    min_confidence: float
    min_estimated_profit: float
    symbols: List[str]
    enabled: bool = True

class SystemMetrics(BaseModel):
    """System performance metrics"""
    timestamp: datetime
    
    # Data collection metrics
    data_points_collected: int
    collection_errors: int
    collection_latency_ms: float
    
    # Detection metrics
    detection_cycles_completed: int
    total_signals_generated: int
    high_confidence_signals: int
    
    # System health
    memory_usage_mb: float
    cpu_usage_percent: float
    active_connections: int
    
class APIResponse(BaseModel):
    """Standard API response model"""
    success: bool
    message: str
    data: Optional[Any] = None
    errors: List[str] = Field(default_factory=list)
    timestamp: datetime = Field(default_factory=datetime.now)

# Database models (SQLAlchemy)
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import UUID
import uuid as uuid_lib

Base = declarative_base()

class ManipulationSignalDB(Base):
    """Database model for manipulation signals"""
    __tablename__ = "manipulation_signals"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid_lib.uuid4)
    pattern_type = Column(String(50), nullable=False)
    timestamp = Column(DateTime, nullable=False)
    symbols_affected = Column(JSON, nullable=False)
    confidence = Column(Float, nullable=False)
    description = Column(Text, nullable=False)
    estimated_profit = Column(Float, nullable=False)
    market_impact = Column(JSON, nullable=False)
    detection_algorithm = Column(String(100), nullable=False)
    raw_data = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow)

class OptionsDataDB(Base):
    """Database model for options data"""
    __tablename__ = "options_data"
    
    id = Column(Integer, primary_key=True)
    symbol = Column(String(20), nullable=False)
    expiry_date = Column(DateTime, nullable=False)
    strike = Column(Float, nullable=False)
    option_type = Column(String(2), nullable=False)
    
    last_price = Column(Float)
    bid_price = Column(Float)
    ask_price = Column(Float)
    volume = Column(Integer)
    open_interest = Column(Integer)
    bid_qty = Column(Integer)
    ask_qty = Column(Integer)
    
    delta = Column(Float)
    gamma = Column(Float)
    theta = Column(Float)
    vega = Column(Float)
    implied_volatility = Column(Float)
    
    timestamp = Column(DateTime, nullable=False)
    change = Column(Float)
    percent_change = Column(Float)

class SystemMetricsDB(Base):
    """Database model for system metrics"""
    __tablename__ = "system_metrics"
    
    id = Column(Integer, primary_key=True)
    timestamp = Column(DateTime, nullable=False)
    
    data_points_collected = Column(Integer)
    collection_errors = Column(Integer)
    collection_latency_ms = Column(Float)
    
    detection_cycles_completed = Column(Integer)
    total_signals_generated = Column(Integer)
    high_confidence_signals = Column(Integer)
    
    memory_usage_mb = Column(Float)
    cpu_usage_percent = Column(Float)
    active_connections = Column(Integer)
