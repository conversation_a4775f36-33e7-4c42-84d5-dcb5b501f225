"""
Main detection engine that orchestrates data collection and manipulation detection
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import time

from data_collectors.nse_collector import NSEDataCollector
from detection.base_detector import detector_registry
from detection.spoofing_detector import SpoofingDetector
from models.data_models import ManipulationSignal, OptionsData, DetectionResult
from utils.database import db_manager
from utils.cache import cache_manager
from utils.metrics import metrics_collector
from config.settings import settings

logger = logging.getLogger(__name__)

class DetectionEngine:
    """
    Main detection engine that coordinates data collection and manipulation detection
    """
    
    def __init__(self):
        self.running = False
        self.data_collector = None
        self.last_detection_time = None
        self.detection_stats = {
            'cycles_completed': 0,
            'total_signals': 0,
            'high_confidence_signals': 0,
            'errors': 0
        }
        
        # Initialize detectors
        self._initialize_detectors()
    
    def _initialize_detectors(self):
        """Initialize and register detection algorithms"""
        try:
            # Register spoofing detector
            spoofing_detector = SpoofingDetector()
            detector_registry.register(spoofing_detector, priority=10)
            
            # TODO: Add other detectors here
            # gamma_detector = GammaSqueezeDetector()
            # detector_registry.register(gamma_detector, priority=8)
            
            # volatility_detector = VolatilitySkewDetector()
            # detector_registry.register(volatility_detector, priority=6)
            
            logger.info(f"Initialized {len(detector_registry.list_detectors())} detectors")
            
        except Exception as e:
            logger.error(f"Error initializing detectors: {str(e)}")
            raise
    
    async def initialize(self):
        """Initialize the detection engine"""
        try:
            # Initialize database
            await db_manager.initialize()
            
            # Initialize cache
            await cache_manager.initialize()
            
            # Initialize data collector
            self.data_collector = NSEDataCollector()
            await self.data_collector.start_session()
            
            # Start metrics server
            metrics_collector.start_prometheus_server()
            
            logger.info("Detection engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize detection engine: {str(e)}")
            raise
    
    async def shutdown(self):
        """Shutdown the detection engine"""
        try:
            self.running = False
            
            # Close data collector
            if self.data_collector:
                await self.data_collector.close_session()
            
            # Close database connections
            await db_manager.close()
            
            # Close cache connections
            await cache_manager.close()
            
            logger.info("Detection engine shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {str(e)}")
    
    async def collect_market_data(self) -> Dict[str, List[OptionsData]]:
        """
        Collect market data from all configured symbols
        
        Returns:
            Dictionary mapping symbols to their options data
        """
        start_time = time.time()
        
        try:
            # Collect data for all symbols
            symbols_data = await self.data_collector.collect_all_symbols(settings.symbols)
            
            # Store data in database
            all_options_data = []
            for symbol, options_list in symbols_data.items():
                all_options_data.extend(options_list)
            
            if all_options_data:
                await db_manager.store_options_data(all_options_data)
            
            # Record metrics
            collection_time = time.time() - start_time
            metrics_collector.record_histogram(
                'data_collection_duration_seconds',
                collection_time,
                {'source': 'nse'}
            )
            
            metrics_collector.increment_counter(
                'data_requests_total',
                {'source': 'nse', 'status': 'success'}
            )
            
            logger.info(
                f"Collected {len(all_options_data)} data points from "
                f"{len(symbols_data)} symbols in {collection_time:.2f}s"
            )
            
            return symbols_data
            
        except Exception as e:
            # Record error metrics
            metrics_collector.increment_counter(
                'data_requests_total',
                {'source': 'nse', 'status': 'error'}
            )
            
            logger.error(f"Error collecting market data: {str(e)}")
            raise
    
    async def run_detection_cycle(self) -> List[ManipulationSignal]:
        """
        Run a complete detection cycle
        
        Returns:
            List of detected manipulation signals
        """
        cycle_start_time = time.time()
        all_signals = []
        
        try:
            logger.info("Starting detection cycle")
            
            # Collect fresh market data
            symbols_data = await self.collect_market_data()
            
            # Combine all options data for detection
            all_options_data = []
            for symbol, options_list in symbols_data.items():
                all_options_data.extend(options_list)
            
            if not all_options_data:
                logger.warning("No options data available for detection")
                return all_signals
            
            # Run all detection algorithms
            detection_results = await detector_registry.run_all_detectors(all_options_data)
            
            # Collect all signals
            for result in detection_results:
                all_signals.extend(result.signals)
                
                # Record detector metrics
                metrics_collector.record_histogram(
                    'detection_duration_seconds',
                    result.execution_time,
                    {'detector_name': result.algorithm_name}
                )
                
                if result.errors:
                    metrics_collector.increment_counter(
                        'detector_errors_total',
                        {'detector_name': result.algorithm_name},
                        len(result.errors)
                    )
            
            # Store signals in database
            if all_signals:
                await db_manager.store_manipulation_signals(all_signals)
            
            # Update statistics
            self.detection_stats['cycles_completed'] += 1
            self.detection_stats['total_signals'] += len(all_signals)
            
            high_conf_signals = [s for s in all_signals if s.confidence >= settings.detection.high_confidence_threshold]
            self.detection_stats['high_confidence_signals'] += len(high_conf_signals)
            
            # Record business metrics
            for signal in all_signals:
                confidence_level = signal.confidence_level.value
                metrics_collector.increment_counter(
                    'signals_generated_total',
                    {
                        'pattern_type': signal.pattern_type.value,
                        'confidence_level': confidence_level
                    }
                )
            
            # Record cycle completion
            cycle_time = time.time() - cycle_start_time
            metrics_collector.increment_counter(
                'detection_cycles_total',
                {'status': 'success'}
            )
            
            self.last_detection_time = datetime.now()
            
            logger.info(
                f"Detection cycle completed: {len(all_signals)} signals "
                f"({len(high_conf_signals)} high confidence) in {cycle_time:.2f}s"
            )
            
            return all_signals
            
        except Exception as e:
            self.detection_stats['errors'] += 1
            
            metrics_collector.increment_counter(
                'detection_cycles_total',
                {'status': 'error'}
            )
            
            logger.error(f"Error in detection cycle: {str(e)}")
            raise
    
    async def process_high_confidence_signals(self, signals: List[ManipulationSignal]):
        """
        Process high confidence signals for immediate action
        
        Args:
            signals: List of manipulation signals
        """
        high_conf_signals = [
            s for s in signals 
            if s.confidence >= settings.detection.high_confidence_threshold
        ]
        
        if not high_conf_signals:
            return
        
        logger.warning(f"HIGH CONFIDENCE MANIPULATION DETECTED: {len(high_conf_signals)} signals")
        
        for signal in high_conf_signals:
            logger.warning(
                f"ALERT: {signal.pattern_type.value} - {signal.description} "
                f"(confidence: {signal.confidence:.2f}, profit: ₹{signal.estimated_profit:,.0f})"
            )
            
            # TODO: Implement alerting system
            # await alert_manager.send_alert(signal)
    
    async def run_continuous(self):
        """
        Run detection engine continuously
        """
        self.running = True
        logger.info("Starting continuous detection engine")
        
        try:
            while self.running:
                try:
                    # Run detection cycle
                    signals = await self.run_detection_cycle()
                    
                    # Process high confidence signals
                    await self.process_high_confidence_signals(signals)
                    
                    # Update system metrics
                    metrics_collector.update_system_metrics()
                    
                    # Store system metrics in database
                    system_stats = metrics_collector.get_summary_stats()
                    await db_manager.store_system_metrics(system_stats)
                    
                    # Wait before next cycle
                    await asyncio.sleep(settings.detection_interval)
                    
                except Exception as e:
                    logger.error(f"Error in detection cycle: {str(e)}")
                    # Wait a bit before retrying
                    await asyncio.sleep(30)
                    
        except KeyboardInterrupt:
            logger.info("Detection engine stopped by user")
        except Exception as e:
            logger.error(f"Fatal error in detection engine: {str(e)}")
            raise
        finally:
            await self.shutdown()
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get detection engine statistics
        
        Returns:
            Dictionary of statistics
        """
        stats = self.detection_stats.copy()
        stats.update({
            'running': self.running,
            'last_detection_time': self.last_detection_time,
            'detector_count': len(detector_registry.list_detectors()),
            'detector_stats': detector_registry.get_all_statistics()
        })
        
        if self.data_collector:
            stats['data_collector_stats'] = self.data_collector.get_statistics()
        
        return stats

# Global detection engine instance
detection_engine = DetectionEngine()
