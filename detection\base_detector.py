"""
Base detector class and plugin architecture for manipulation detection
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging
import time
import asyncio

from models.data_models import ManipulationSignal, OptionsData, DetectionResult
from utils.database import DatabaseManager
from utils.metrics import MetricsCollector

logger = logging.getLogger(__name__)

class BaseDetector(ABC):
    """
    Abstract base class for all manipulation detectors
    """
    
    def __init__(self, name: str, config: Dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.enabled = self.config.get("enabled", True)
        self.metrics = MetricsCollector()
        
        # Performance tracking
        self.execution_count = 0
        self.total_execution_time = 0.0
        self.last_execution_time = None
        self.signals_generated = 0
        
    @abstractmethod
    async def detect(self, data: List[OptionsData]) -> List[ManipulationSignal]:
        """
        Main detection method - must be implemented by subclasses
        
        Args:
            data: List of options data to analyze
            
        Returns:
            List of detected manipulation signals
        """
        pass
    
    @abstractmethod
    def get_required_data_window(self) -> int:
        """
        Return the minimum number of data points required for detection
        
        Returns:
            Number of data points needed
        """
        pass
    
    async def run_detection(self, data: List[OptionsData]) -> DetectionResult:
        """
        Run detection with error handling and metrics collection
        
        Args:
            data: Options data to analyze
            
        Returns:
            Detection result with signals and metadata
        """
        if not self.enabled:
            return DetectionResult(
                algorithm_name=self.name,
                execution_time=0.0,
                signals_found=0,
                signals=[],
                metadata={"status": "disabled"}
            )
        
        start_time = time.time()
        signals = []
        errors = []
        
        try:
            # Validate input data
            if len(data) < self.get_required_data_window():
                raise ValueError(
                    f"Insufficient data: need {self.get_required_data_window()}, got {len(data)}"
                )
            
            # Run detection
            signals = await self.detect(data)
            
            # Update statistics
            self.signals_generated += len(signals)
            
            logger.info(
                f"{self.name} detected {len(signals)} signals in "
                f"{time.time() - start_time:.3f}s"
            )
            
        except Exception as e:
            error_msg = f"Detection error in {self.name}: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)
            
            # Record error metric
            self.metrics.increment_counter(f"detector_errors_{self.name}")
        
        finally:
            # Update performance metrics
            execution_time = time.time() - start_time
            self.execution_count += 1
            self.total_execution_time += execution_time
            self.last_execution_time = datetime.now()
            
            # Record performance metrics
            self.metrics.record_histogram(f"detector_execution_time_{self.name}", execution_time)
            self.metrics.record_gauge(f"detector_signals_{self.name}", len(signals))
        
        return DetectionResult(
            algorithm_name=self.name,
            execution_time=execution_time,
            signals_found=len(signals),
            signals=signals,
            errors=errors,
            metadata={
                "data_points_analyzed": len(data),
                "execution_count": self.execution_count,
                "average_execution_time": self.total_execution_time / self.execution_count
            }
        )
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get detector performance statistics
        
        Returns:
            Dictionary with performance metrics
        """
        return {
            "name": self.name,
            "enabled": self.enabled,
            "execution_count": self.execution_count,
            "total_execution_time": self.total_execution_time,
            "average_execution_time": self.total_execution_time / max(self.execution_count, 1),
            "last_execution_time": self.last_execution_time,
            "signals_generated": self.signals_generated,
            "signals_per_execution": self.signals_generated / max(self.execution_count, 1)
        }
    
    def update_config(self, new_config: Dict[str, Any]):
        """
        Update detector configuration
        
        Args:
            new_config: New configuration parameters
        """
        self.config.update(new_config)
        self.enabled = self.config.get("enabled", True)
        logger.info(f"Updated configuration for {self.name}")

class DetectorRegistry:
    """
    Registry for managing detection algorithms
    """
    
    def __init__(self):
        self.detectors: Dict[str, BaseDetector] = {}
        self.execution_order: List[str] = []
    
    def register(self, detector: BaseDetector, priority: int = 0):
        """
        Register a detector
        
        Args:
            detector: Detector instance to register
            priority: Execution priority (higher = earlier)
        """
        self.detectors[detector.name] = detector
        
        # Insert in priority order
        inserted = False
        for i, name in enumerate(self.execution_order):
            existing_detector = self.detectors[name]
            existing_priority = getattr(existing_detector, 'priority', 0)
            if priority > existing_priority:
                self.execution_order.insert(i, detector.name)
                inserted = True
                break
        
        if not inserted:
            self.execution_order.append(detector.name)
        
        logger.info(f"Registered detector: {detector.name} with priority {priority}")
    
    def unregister(self, detector_name: str):
        """
        Unregister a detector
        
        Args:
            detector_name: Name of detector to remove
        """
        if detector_name in self.detectors:
            del self.detectors[detector_name]
            self.execution_order.remove(detector_name)
            logger.info(f"Unregistered detector: {detector_name}")
    
    def get_detector(self, name: str) -> Optional[BaseDetector]:
        """
        Get detector by name
        
        Args:
            name: Detector name
            
        Returns:
            Detector instance or None
        """
        return self.detectors.get(name)
    
    def list_detectors(self) -> List[str]:
        """
        Get list of registered detector names
        
        Returns:
            List of detector names in execution order
        """
        return self.execution_order.copy()
    
    async def run_all_detectors(self, data: List[OptionsData]) -> List[DetectionResult]:
        """
        Run all registered detectors
        
        Args:
            data: Options data to analyze
            
        Returns:
            List of detection results
        """
        results = []
        
        # Run detectors in parallel
        tasks = []
        for detector_name in self.execution_order:
            detector = self.detectors[detector_name]
            if detector.enabled:
                task = asyncio.create_task(
                    detector.run_detection(data),
                    name=f"detect_{detector_name}"
                )
                tasks.append(task)
        
        # Wait for all detectors to complete
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle exceptions
            valid_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    detector_name = self.execution_order[i]
                    logger.error(f"Detector {detector_name} failed: {str(result)}")
                    # Create error result
                    valid_results.append(DetectionResult(
                        algorithm_name=detector_name,
                        execution_time=0.0,
                        signals_found=0,
                        signals=[],
                        errors=[str(result)]
                    ))
                else:
                    valid_results.append(result)
            
            results = valid_results
        
        return results
    
    def get_all_statistics(self) -> Dict[str, Dict[str, Any]]:
        """
        Get statistics for all detectors
        
        Returns:
            Dictionary mapping detector names to their statistics
        """
        return {
            name: detector.get_statistics()
            for name, detector in self.detectors.items()
        }
    
    def enable_detector(self, detector_name: str):
        """Enable a specific detector"""
        if detector_name in self.detectors:
            self.detectors[detector_name].enabled = True
            logger.info(f"Enabled detector: {detector_name}")
    
    def disable_detector(self, detector_name: str):
        """Disable a specific detector"""
        if detector_name in self.detectors:
            self.detectors[detector_name].enabled = False
            logger.info(f"Disabled detector: {detector_name}")

# Global detector registry
detector_registry = DetectorRegistry()
