"""
Configuration settings for the Options Manipulation Detection System
"""
from pydantic_settings import BaseSettings
from pydantic import Field
from typing import List, Dict, Optional
import os

class DatabaseSettings(BaseSettings):
    """Database configuration"""
    url: str = Field(default="postgresql://user:password@localhost:5432/options_db")
    pool_size: int = Field(default=20)
    max_overflow: int = Field(default=30)
    pool_timeout: int = Field(default=30)
    
    class Config:
        env_prefix = "DB_"

class RedisSettings(BaseSettings):
    """Redis configuration for caching"""
    host: str = Field(default="localhost")
    port: int = Field(default=6379)
    db: int = Field(default=0)
    password: Optional[str] = Field(default=None)
    
    class Config:
        env_prefix = "REDIS_"

class NSEAPISettings(BaseSettings):
    """NSE API configuration"""
    base_url: str = Field(default="https://www.nseindia.com/api")
    options_endpoint: str = Field(default="/option-chain-indices")
    historical_endpoint: str = Field(default="/historical/cm/equity")
    market_status_endpoint: str = Field(default="/marketStatus")

    # Optional API key for premium providers (backup)
    api_key: Optional[str] = Field(default=None)
    api_secret: Optional[str] = Field(default=None)

    # Rate limiting
    requests_per_minute: int = Field(default=60)
    requests_per_second: int = Field(default=2)
    
    # Headers for NSE API
    headers: Dict[str, str] = Field(default={
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    })
    
    class Config:
        env_prefix = "NSE_"

class DetectionSettings(BaseSettings):
    """Detection algorithm configuration"""
    # Spoofing detection
    spoof_qty_threshold: int = Field(default=1000)
    spoof_time_window_seconds: int = Field(default=30)
    
    # Gamma squeeze detection
    gamma_exposure_threshold: float = Field(default=1000000.0)
    gamma_volume_ratio: float = Field(default=0.5)
    
    # Volatility manipulation
    iv_change_threshold: float = Field(default=0.05)
    volume_percentile_threshold: float = Field(default=0.8)
    
    # Cross-asset manipulation
    spread_zscore_threshold: float = Field(default=2.0)
    correlation_window: int = Field(default=10)
    
    # Confidence thresholds
    high_confidence_threshold: float = Field(default=0.8)
    medium_confidence_threshold: float = Field(default=0.6)
    
    class Config:
        env_prefix = "DETECTION_"

class MonitoringSettings(BaseSettings):
    """Monitoring and alerting configuration"""
    prometheus_port: int = Field(default=8000)
    log_level: str = Field(default="INFO")
    sentry_dsn: Optional[str] = Field(default=None)
    
    # Alert thresholds
    alert_confidence_threshold: float = Field(default=0.8)
    alert_profit_threshold: float = Field(default=100000.0)
    
    class Config:
        env_prefix = "MONITORING_"

class APISettings(BaseSettings):
    """API server configuration"""
    host: str = Field(default="0.0.0.0")
    port: int = Field(default=8080)
    workers: int = Field(default=4)
    reload: bool = Field(default=False)
    
    class Config:
        env_prefix = "API_"

class CelerySettings(BaseSettings):
    """Celery configuration for background tasks"""
    broker_url: str = Field(default="redis://localhost:6379/1")
    result_backend: str = Field(default="redis://localhost:6379/2")
    task_serializer: str = Field(default="json")
    result_serializer: str = Field(default="json")
    accept_content: List[str] = Field(default=["json"])
    timezone: str = Field(default="Asia/Kolkata")
    
    class Config:
        env_prefix = "CELERY_"

class Settings(BaseSettings):
    """Main settings class"""
    # Environment
    environment: str = Field(default="development")
    debug: bool = Field(default=True)
    
    # Component settings
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    redis: RedisSettings = Field(default_factory=RedisSettings)
    nse_api: NSEAPISettings = Field(default_factory=NSEAPISettings)
    detection: DetectionSettings = Field(default_factory=DetectionSettings)
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)
    api: APISettings = Field(default_factory=APISettings)
    celery: CelerySettings = Field(default_factory=CelerySettings)
    
    # Symbols to monitor
    symbols: List[str] = Field(default=["NIFTY", "BANKNIFTY", "FINNIFTY"])
    
    # Data collection intervals
    data_collection_interval: int = Field(default=30)  # seconds
    detection_interval: int = Field(default=60)  # seconds
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

# Global settings instance
settings = Settings()

# NSE API URLs
NSE_URLS = {
    "options": f"{settings.nse_api.base_url}{settings.nse_api.options_endpoint}",
    "historical": f"{settings.nse_api.base_url}{settings.nse_api.historical_endpoint}",
    "market_status": f"{settings.nse_api.base_url}{settings.nse_api.market_status_endpoint}"
}
