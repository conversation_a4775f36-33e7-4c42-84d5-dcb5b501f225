"""
Metrics collection and monitoring utilities
"""
import time
from typing import Dict, Any, Optional
from collections import defaultdict, deque
import threading
import psutil
import logging

from prometheus_client import Counter, Histogram, Gauge, start_http_server
from config.settings import settings

logger = logging.getLogger(__name__)

class MetricsCollector:
    """
    Prometheus metrics collector for system monitoring
    """
    
    def __init__(self):
        # Prometheus metrics
        self.counters = {}
        self.histograms = {}
        self.gauges = {}
        
        # Initialize common metrics
        self._initialize_metrics()
        
        # In-memory metrics for quick access
        self.memory_metrics = defaultdict(lambda: deque(maxlen=1000))
        self._lock = threading.Lock()
        
        # System metrics
        self.process = psutil.Process()
        
    def _initialize_metrics(self):
        """Initialize Prometheus metrics"""
        
        # Data collection metrics
        try:
            self.counters['data_requests_total'] = Counter(
                'data_requests_total',
                'Total number of data requests',
                ['source', 'status']
            )
        except ValueError:
            # Metric already exists, get existing one
            from prometheus_client import REGISTRY
            for collector in list(REGISTRY._collector_to_names.keys()):
                if hasattr(collector, '_name') and 'data_requests_total' in collector._name:
                    self.counters['data_requests_total'] = collector
                    break
        
        self.counters['detection_cycles_total'] = Counter(
            'detection_cycles_total',
            'Total number of detection cycles',
            ['status']
        )
        
        self.counters['signals_generated_total'] = Counter(
            'signals_generated_total',
            'Total number of manipulation signals generated',
            ['pattern_type', 'confidence_level']
        )
        
        self.counters['detector_errors_total'] = Counter(
            'detector_errors_total',
            'Total number of detector errors',
            ['detector_name']
        )
        
        # Performance metrics
        self.histograms['data_collection_duration_seconds'] = Histogram(
            'data_collection_duration_seconds',
            'Time spent collecting data',
            ['source']
        )
        
        self.histograms['detection_duration_seconds'] = Histogram(
            'detection_duration_seconds',
            'Time spent running detection algorithms',
            ['detector_name']
        )
        
        self.histograms['database_operation_duration_seconds'] = Histogram(
            'database_operation_duration_seconds',
            'Time spent on database operations',
            ['operation']
        )
        
        # System metrics
        self.gauges['memory_usage_bytes'] = Gauge(
            'memory_usage_bytes',
            'Memory usage in bytes'
        )
        
        self.gauges['cpu_usage_percent'] = Gauge(
            'cpu_usage_percent',
            'CPU usage percentage'
        )
        
        self.gauges['active_connections'] = Gauge(
            'active_connections',
            'Number of active database connections'
        )
        
        self.gauges['cache_hit_rate'] = Gauge(
            'cache_hit_rate',
            'Cache hit rate percentage'
        )
        
        # Business metrics
        self.gauges['high_confidence_signals'] = Gauge(
            'high_confidence_signals',
            'Number of high confidence signals in last hour'
        )
        
        self.gauges['estimated_manipulation_profit'] = Gauge(
            'estimated_manipulation_profit',
            'Estimated manipulation profit in last hour'
        )
    
    def increment_counter(self, name: str, labels: Dict[str, str] = None, amount: float = 1):
        """
        Increment a counter metric
        
        Args:
            name: Counter name
            labels: Label dictionary
            amount: Amount to increment by
        """
        try:
            if name in self.counters:
                if labels:
                    self.counters[name].labels(**labels).inc(amount)
                else:
                    self.counters[name].inc(amount)
            
            # Also store in memory for quick access
            with self._lock:
                self.memory_metrics[f"counter_{name}"].append({
                    'timestamp': time.time(),
                    'value': amount,
                    'labels': labels or {}
                })
                
        except Exception as e:
            logger.warning(f"Error incrementing counter {name}: {str(e)}")
    
    def record_histogram(self, name: str, value: float, labels: Dict[str, str] = None):
        """
        Record a histogram observation
        
        Args:
            name: Histogram name
            value: Value to observe
            labels: Label dictionary
        """
        try:
            if name in self.histograms:
                if labels:
                    self.histograms[name].labels(**labels).observe(value)
                else:
                    self.histograms[name].observe(value)
            
            # Also store in memory
            with self._lock:
                self.memory_metrics[f"histogram_{name}"].append({
                    'timestamp': time.time(),
                    'value': value,
                    'labels': labels or {}
                })
                
        except Exception as e:
            logger.warning(f"Error recording histogram {name}: {str(e)}")
    
    def record_gauge(self, name: str, value: float, labels: Dict[str, str] = None):
        """
        Set a gauge value
        
        Args:
            name: Gauge name
            value: Value to set
            labels: Label dictionary
        """
        try:
            if name in self.gauges:
                if labels:
                    self.gauges[name].labels(**labels).set(value)
                else:
                    self.gauges[name].set(value)
            
            # Also store in memory
            with self._lock:
                self.memory_metrics[f"gauge_{name}"].append({
                    'timestamp': time.time(),
                    'value': value,
                    'labels': labels or {}
                })
                
        except Exception as e:
            logger.warning(f"Error setting gauge {name}: {str(e)}")
    
    def update_system_metrics(self):
        """Update system performance metrics"""
        try:
            # Memory usage
            memory_info = self.process.memory_info()
            self.record_gauge('memory_usage_bytes', memory_info.rss)
            
            # CPU usage
            cpu_percent = self.process.cpu_percent()
            self.record_gauge('cpu_usage_percent', cpu_percent)
            
            # System-wide metrics
            system_memory = psutil.virtual_memory()
            self.record_gauge('system_memory_usage_percent', system_memory.percent)
            
            system_cpu = psutil.cpu_percent()
            self.record_gauge('system_cpu_usage_percent', system_cpu)
            
        except Exception as e:
            logger.warning(f"Error updating system metrics: {str(e)}")
    
    def get_memory_metrics(self, metric_name: str, minutes: int = 5) -> list:
        """
        Get in-memory metrics for the last N minutes
        
        Args:
            metric_name: Name of the metric
            minutes: Number of minutes to look back
            
        Returns:
            List of metric data points
        """
        cutoff_time = time.time() - (minutes * 60)
        
        with self._lock:
            metrics = self.memory_metrics.get(metric_name, deque())
            return [m for m in metrics if m['timestamp'] >= cutoff_time]
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """
        Get summary statistics for monitoring dashboard
        
        Returns:
            Dictionary of summary statistics
        """
        stats = {}
        
        try:
            # System metrics
            self.update_system_metrics()
            
            # Get recent counter values
            data_requests = self.get_memory_metrics('counter_data_requests_total', 60)
            detection_cycles = self.get_memory_metrics('counter_detection_cycles_total', 60)
            signals_generated = self.get_memory_metrics('counter_signals_generated_total', 60)
            
            stats.update({
                'data_requests_last_hour': len(data_requests),
                'detection_cycles_last_hour': len(detection_cycles),
                'signals_generated_last_hour': len(signals_generated),
                'memory_usage_mb': self.process.memory_info().rss / 1024 / 1024,
                'cpu_usage_percent': self.process.cpu_percent(),
                'uptime_seconds': time.time() - self.process.create_time()
            })
            
            # Get recent histogram averages
            collection_times = self.get_memory_metrics('histogram_data_collection_duration_seconds', 60)
            if collection_times:
                avg_collection_time = sum(m['value'] for m in collection_times) / len(collection_times)
                stats['avg_collection_time_seconds'] = avg_collection_time
            
            detection_times = self.get_memory_metrics('histogram_detection_duration_seconds', 60)
            if detection_times:
                avg_detection_time = sum(m['value'] for m in detection_times) / len(detection_times)
                stats['avg_detection_time_seconds'] = avg_detection_time
            
        except Exception as e:
            logger.error(f"Error getting summary stats: {str(e)}")
        
        return stats
    
    def start_prometheus_server(self, port: Optional[int] = None):
        """
        Start Prometheus metrics server
        
        Args:
            port: Port to start server on (defaults to config value)
        """
        try:
            server_port = port or settings.monitoring.prometheus_port
            start_http_server(server_port)
            logger.info(f"Prometheus metrics server started on port {server_port}")
            
        except Exception as e:
            logger.error(f"Failed to start Prometheus server: {str(e)}")

# Global metrics collector instance
metrics_collector = MetricsCollector()

# Convenience functions
def increment_counter(name: str, labels: Dict[str, str] = None, amount: float = 1):
    """Increment a counter metric"""
    metrics_collector.increment_counter(name, labels, amount)

def record_histogram(name: str, value: float, labels: Dict[str, str] = None):
    """Record a histogram observation"""
    metrics_collector.record_histogram(name, value, labels)

def record_gauge(name: str, value: float, labels: Dict[str, str] = None):
    """Set a gauge value"""
    metrics_collector.record_gauge(name, value, labels)

def update_system_metrics():
    """Update system performance metrics"""
    metrics_collector.update_system_metrics()

def get_summary_stats() -> Dict[str, Any]:
    """Get summary statistics"""
    return metrics_collector.get_summary_stats()
