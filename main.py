"""
Main entry point for the Options Manipulation Detection System
"""
import asyncio
import logging
import signal
import sys
from typing import Optional

from core.detection_engine import detection_engine
from config.settings import settings

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.monitoring.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('options_detection.log')
    ]
)

logger = logging.getLogger(__name__)

class OptionsDetectionSystem:
    """
    Main system orchestrator
    """
    
    def __init__(self):
        self.running = False
        self.shutdown_event = asyncio.Event()
    
    async def start(self):
        """Start the detection system"""
        try:
            logger.info("Starting Options Manipulation Detection System")
            logger.info(f"Environment: {settings.environment}")
            logger.info(f"Monitoring symbols: {settings.symbols}")
            
            # Initialize detection engine
            await detection_engine.initialize()
            
            # Set up signal handlers for graceful shutdown
            self._setup_signal_handlers()
            
            self.running = True
            logger.info("System started successfully")
            
            # Run detection engine
            await detection_engine.run_continuous()
            
        except KeyboardInterrupt:
            logger.info("Shutdown requested by user")
        except Exception as e:
            logger.error(f"Fatal error: {str(e)}")
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Shutdown the system gracefully"""
        if not self.running:
            return
        
        logger.info("Shutting down Options Manipulation Detection System")
        self.running = False
        
        try:
            # Shutdown detection engine
            await detection_engine.shutdown()
            
            # Set shutdown event
            self.shutdown_event.set()
            
            logger.info("System shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {str(e)}")
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

async def run_api_server():
    """Run the API server"""
    import uvicorn
    from api.main import app
    
    config = uvicorn.Config(
        app,
        host=settings.api.host,
        port=settings.api.port,
        workers=1,  # Use 1 worker for development
        reload=settings.api.reload,
        log_level=settings.monitoring.log_level.lower()
    )
    
    server = uvicorn.Server(config)
    await server.serve()

async def run_detection_only():
    """Run only the detection engine without API"""
    system = OptionsDetectionSystem()
    await system.start()

async def run_full_system():
    """Run both detection engine and API server"""
    # Start detection engine in background
    detection_task = asyncio.create_task(run_detection_only())
    
    # Start API server
    api_task = asyncio.create_task(run_api_server())
    
    # Wait for either to complete
    done, pending = await asyncio.wait(
        [detection_task, api_task],
        return_when=asyncio.FIRST_COMPLETED
    )
    
    # Cancel remaining tasks
    for task in pending:
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Options Manipulation Detection System")
    parser.add_argument(
        "--mode",
        choices=["detection", "api", "full"],
        default="full",
        help="Run mode: detection only, API only, or full system"
    )
    parser.add_argument(
        "--config",
        help="Path to configuration file"
    )
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level"
    )
    
    args = parser.parse_args()
    
    # Update log level if specified
    if args.log_level:
        logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # Load custom config if specified
    if args.config:
        # TODO: Implement config file loading
        logger.info(f"Loading config from {args.config}")
    
    try:
        if args.mode == "detection":
            logger.info("Starting in detection-only mode")
            asyncio.run(run_detection_only())
        elif args.mode == "api":
            logger.info("Starting in API-only mode")
            asyncio.run(run_api_server())
        else:
            logger.info("Starting in full system mode")
            asyncio.run(run_full_system())
            
    except KeyboardInterrupt:
        logger.info("System stopped by user")
    except Exception as e:
        logger.error(f"System failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
